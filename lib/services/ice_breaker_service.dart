import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import '../models/user.dart';

class IceBreakerService {
  static final IceBreakerService _instance = IceBreakerService._internal();
  factory IceBreakerService() => _instance;
  IceBreakerService._internal();

  Map<String, dynamic>? _iceBreakerData;

  /// 加载破冰话题数据
  Future<void> loadIceBreakerData() async {
    if (_iceBreakerData != null) return;

    try {
      final String jsonString = await rootBundle.loadString('assets/data/ice_breaker_topics.json');
      _iceBreakerData = json.decode(jsonString);
    } catch (e) {
      debugPrint('Error loading ice breaker data: $e');
      _iceBreakerData = {};
    }
  }

  /// 获取当前语言代码
  String _getLanguageCode(Locale locale) {
    switch (locale.languageCode) {
      case 'zh':
        return 'zh';
      case 'ar':
        return 'ar';
      case 'hi':
        return 'hi';
      default:
        return 'en';
    }
  }

  /// 获取通用破冰话题
  Future<List<String>> getGeneralTopics(Locale locale) async {
    await loadIceBreakerData();
    
    final languageCode = _getLanguageCode(locale);
    final generalTopics = _iceBreakerData?['general_topics']?[languageCode] as List<dynamic>?;
    
    return generalTopics?.cast<String>() ?? [];
  }

  /// 获取角色特定的破冰话题
  Future<List<String>> getRoleSpecificTopics(User aiUser, Locale locale) async {
    await loadIceBreakerData();
    
    final languageCode = _getLanguageCode(locale);
    final roleTopics = _iceBreakerData?['role_specific_topics']?[aiUser.role]?[languageCode] as List<dynamic>?;
    
    return roleTopics?.cast<String>() ?? [];
  }

  /// 获取混合的破冰话题（通用 + 角色特定）
  Future<List<String>> getMixedTopics(User aiUser, Locale locale) async {
    await loadIceBreakerData();
    
    final generalTopics = await getGeneralTopics(locale);
    final roleTopics = await getRoleSpecificTopics(aiUser, locale);
    
    // 混合话题：2个通用 + 3个角色特定（如果有的话）
    final mixedTopics = <String>[];
    
    // 添加前2个通用话题
    if (generalTopics.isNotEmpty) {
      mixedTopics.addAll(generalTopics.take(2));
    }
    
    // 添加角色特定话题
    if (roleTopics.isNotEmpty) {
      mixedTopics.addAll(roleTopics.take(3));
    } else {
      // 如果没有角色特定话题，添加更多通用话题
      if (generalTopics.length > 2) {
        mixedTopics.addAll(generalTopics.skip(2).take(3));
      }
    }
    
    return mixedTopics;
  }

  /// 获取推荐的破冰话题（最多5个）
  Future<List<String>> getRecommendedTopics(User aiUser, Locale locale) async {
    final mixedTopics = await getMixedTopics(aiUser, locale);
    return mixedTopics.take(5).toList();
  }
}

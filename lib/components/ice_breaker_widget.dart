import 'package:flutter/material.dart';
import '../models/user.dart';
import '../services/ice_breaker_service.dart';
import '../theme/app_design_system.dart';

class IceBreakerWidget extends StatefulWidget {
  final User aiUser;
  final Function(String) onTopicSelected;
  final bool isVisible;

  const IceBreakerWidget({
    super.key,
    required this.aiUser,
    required this.onTopicSelected,
    this.isVisible = true,
  });

  @override
  State<IceBreakerWidget> createState() => _IceBreakerWidgetState();
}

class _IceBreakerWidgetState extends State<IceBreakerWidget>
    with SingleTickerProviderStateMixin {
  List<String> _topics = [];
  bool _isLoading = true;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _loadTopics();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadTopics() async {
    try {
      final locale = Localizations.localeOf(context);
      final topics = await IceBreakerService().getRecommendedTopics(widget.aiUser, locale);
      
      if (mounted) {
        setState(() {
          _topics = topics;
          _isLoading = false;
        });
        
        if (widget.isVisible) {
          _animationController.forward();
        }
      }
    } catch (e) {
      debugPrint('Error loading ice breaker topics: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void didUpdateWidget(IceBreakerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isVisible != oldWidget.isVisible) {
      if (widget.isVisible) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
    
    if (widget.aiUser.id != oldWidget.aiUser.id) {
      _loadTopics();
    }
  }

  void _onTopicTap(String topic) {
    // 添加点击动画效果
    widget.onTopicSelected(topic);
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible || _isLoading || _topics.isEmpty) {
      return const SizedBox.shrink();
    }

    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Container(
          height: 50,
          margin: const EdgeInsets.only(bottom: 12),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: _topics.length,
            itemBuilder: (context, index) {
              return _buildTopicBubble(_topics[index], index);
            },
          ),
        ),
      ),
    );
  }

  Widget _buildTopicBubble(String topic, int index) {
    return Container(
      margin: EdgeInsets.only(
        right: index < _topics.length - 1 ? 12 : 0,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _onTopicTap(topic),
          borderRadius: BorderRadius.circular(25),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.white.withOpacity(0.9),
                  Colors.white.withOpacity(0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(25),
              border: Border.all(
                color: AppDesignSystem.primaryYellow.withOpacity(0.3),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.chat_bubble_outline,
                  size: 16,
                  color: AppDesignSystem.textPrimary.withOpacity(0.7),
                ),
                const SizedBox(width: 6),
                ConstrainedBox(
                  constraints: BoxConstraints(
                    maxWidth: MediaQuery.of(context).size.width * 0.6,
                  ),
                  child: Text(
                    topic,
                    style: TextStyle(
                      color: AppDesignSystem.textPrimary,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 简化版的破冰话题组件，用于快速集成
class SimpleIceBreakerWidget extends StatelessWidget {
  final User aiUser;
  final Function(String) onTopicSelected;

  const SimpleIceBreakerWidget({
    super.key,
    required this.aiUser,
    required this.onTopicSelected,
  });

  @override
  Widget build(BuildContext context) {
    return IceBreakerWidget(
      aiUser: aiUser,
      onTopicSelected: onTopicSelected,
      isVisible: true,
    );
  }
}
